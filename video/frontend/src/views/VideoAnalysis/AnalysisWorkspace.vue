<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center">
          <h1 class="text-3xl font-bold text-gray-900">视频分析工作台</h1>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- 视频列表 -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow">
            <div class="p-4 border-b">
              <h3 class="text-lg font-medium text-gray-900">视频列表</h3>
            </div>
            <div class="p-4">
              <!-- 加载状态 -->
              <div v-if="loadingVideos" class="text-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                <p class="mt-2 text-sm text-gray-500">加载中...</p>
              </div>
              
              <!-- 视频列表 -->
              <div v-else-if="videos.length > 0" class="space-y-3">
                <div
                  v-for="video in videos"
                  :key="video.id"
                  class="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                  :class="{ 'border-primary-500 bg-primary-50': selectedVideo?.id === video.id }"
                  @click="selectVideo(video)"
                >
                  <div class="flex items-center space-x-3">
                    <div class="w-12 h-8 bg-gray-200 rounded flex-shrink-0">
                      <img
                        v-if="video.thumbnail"
                        :src="video.thumbnail"
                        :alt="video.original_filename"
                        class="w-full h-full object-cover rounded"
                      />
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 truncate">{{ video.original_filename }}</p>
                      <p class="text-xs text-gray-500">{{ formatDuration(video.duration) }}</p>
                    </div>
                  </div>
                  
                  <!-- 分析状态 -->
                  <div class="mt-2">
                    <div class="flex justify-between text-xs text-gray-500 mb-1">
                      <span>分析进度</span>
                      <span>{{ Math.round(video.analysis_progress || 0) }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1">
                      <div
                        class="bg-primary-600 h-1 rounded-full transition-all"
                        :style="{ width: `${video.analysis_progress || 0}%` }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 空状态 -->
              <div v-else class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">暂无视频</h3>
                <p class="mt-1 text-xs text-gray-500">上传视频开始分析</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 分析结果展示区域 -->
        <div class="lg:col-span-3">
          <!-- 未选择视频 -->
          <div v-if="!selectedVideo" class="bg-white rounded-lg shadow p-8 text-center">
            <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h3 class="mt-4 text-lg font-medium text-gray-900">选择视频开始分析</h3>
            <p class="mt-2 text-gray-500">从左侧列表中选择一个视频查看分析结果</p>
          </div>

          <!-- 视频分析结果 -->
          <div v-else class="space-y-6">
            <!-- 视频预览 -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
              <div class="aspect-video bg-black relative">
                <!-- 视频播放器 -->
                <video
                  v-if="selectedVideo.original_filename"
                  ref="videoPlayer"
                  class="video-player w-full h-full object-contain"
                  controls
                  preload="metadata"
                  @loadedmetadata="onVideoLoaded"
                  @error="onVideoError"
                >
                  <source :src="getVideoUrl(selectedVideo)" type="video/mp4">
                  <p class="text-white text-center">您的浏览器不支持视频播放</p>
                </video>

                <!-- 加载状态 -->
                <div v-if="videoLoading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                  <div class="text-center text-white">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                    <p>加载视频中...</p>
                  </div>
                </div>

                <!-- 错误状态 -->
                <div v-else-if="videoError" class="w-full h-full flex items-center justify-center text-white">
                  <div class="text-center">
                    <svg class="mx-auto h-16 w-16 text-red-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <p class="text-lg mb-2">视频加载失败</p>
                    <p class="text-sm text-gray-400">{{ selectedVideo.original_filename }}</p>
                    <button
                      @click="retryVideoLoad"
                      class="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm"
                    >
                      重试
                    </button>
                  </div>
                </div>

                <!-- 默认状态（无视频文件） -->
                <div v-else-if="!selectedVideo.filename" class="w-full h-full flex items-center justify-center text-white">
                  <div class="text-center">
                    <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h10a2 2 0 012 2v8a2 2 0 01-2 2H6a2 2 0 01-2-2v-8a2 2 0 012-2z" />
                    </svg>
                    <p class="text-lg">{{ selectedVideo.original_filename }}</p>
                    <p class="text-sm text-gray-400">{{ formatDuration(selectedVideo.duration) }}</p>
                  </div>
                </div>
              </div>
              
              <div class="p-4">
                <div class="flex justify-between items-center">
                  <h3 class="text-lg font-medium text-gray-900">{{ selectedVideo.original_filename }}</h3>
                  <div class="flex space-x-2">
                    <button
                      @click="startAnalysis"
                      :disabled="selectedVideo.analysis_progress > 0"
                      class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      {{ selectedVideo.analysis_progress > 0 ? '分析中...' : '开始分析' }}
                    </button>
                    <button
                      @click="viewAnalysisDetail"
                      class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      查看详情
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分析结果卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 基础信息分析 -->
              <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-lg font-medium text-gray-900">基础信息</h4>
                  <div
                    class="w-3 h-3 rounded-full"
                    :class="getStatusColor(selectedVideo.basic_analysis_status)"
                  ></div>
                </div>
                <div v-if="selectedVideo.basic_info" class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-500">分辨率</span>
                    <span>{{ selectedVideo.basic_info.resolution }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">帧率</span>
                    <span>{{ selectedVideo.basic_info.fps }}fps</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">编码</span>
                    <span>{{ selectedVideo.basic_info.codec }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">场景数</span>
                    <span>{{ selectedVideo.basic_info.scene_count }}</span>
                  </div>
                </div>
                <div v-else class="text-sm text-gray-500">
                  {{ getStatusText(selectedVideo.basic_analysis_status) }}
                </div>
              </div>

              <!-- 内容要素分析 -->
              <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-lg font-medium text-gray-900">内容要素</h4>
                  <div
                    class="w-3 h-3 rounded-full"
                    :class="getStatusColor(selectedVideo.content_analysis_status)"
                  ></div>
                </div>
                <div v-if="selectedVideo.content_analysis" class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-500">人物数量</span>
                    <span>{{ selectedVideo.content_analysis.character_count }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">场景类型</span>
                    <span>{{ selectedVideo.content_analysis.scene_types }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">主要情绪</span>
                    <span>{{ selectedVideo.content_analysis.dominant_emotion }}</span>
                  </div>
                </div>
                <div v-else class="text-sm text-gray-500">
                  {{ getStatusText(selectedVideo.content_analysis_status) }}
                </div>
              </div>

              <!-- 剧情分析 -->
              <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-lg font-medium text-gray-900">剧情分析</h4>
                  <div
                    class="w-3 h-3 rounded-full"
                    :class="getStatusColor(selectedVideo.plot_analysis_status)"
                  ></div>
                </div>
                <div v-if="selectedVideo.plot_analysis" class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-500">对话片段</span>
                    <span>{{ selectedVideo.plot_analysis.dialogue_segments }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">情节转折</span>
                    <span>{{ selectedVideo.plot_analysis.plot_points }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">高潮片段</span>
                    <span>{{ selectedVideo.plot_analysis.climax_segments }}</span>
                  </div>
                </div>
                <div v-else class="text-sm text-gray-500">
                  {{ getStatusText(selectedVideo.plot_analysis_status) }}
                </div>
              </div>
            </div>

            <!-- 分析进度 -->
            <div class="bg-white rounded-lg shadow p-6">
              <h4 class="text-lg font-medium text-gray-900 mb-4">分析进度</h4>
              <div class="space-y-4">
                <div
                  v-for="step in analysisSteps"
                  :key="step.id"
                  class="flex items-center justify-between"
                >
                  <div class="flex items-center">
                    <div
                      class="w-4 h-4 rounded-full mr-3"
                      :class="getStatusColor(selectedVideo[step.statusKey])"
                    ></div>
                    <span class="text-sm font-medium">{{ step.name }}</span>
                  </div>
                  <span class="text-sm text-gray-500">
                    {{ getStatusText(selectedVideo[step.statusKey]) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传视频模态框 -->
    <div
      v-if="showUploadModal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="showUploadModal = false"
    >
      <div
        class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">上传视频</h3>
          
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <div class="mt-4">
              <label class="cursor-pointer">
                <span class="mt-2 block text-sm font-medium text-gray-900">
                  点击上传或拖拽文件到此处
                </span>
                <input type="file" class="sr-only" multiple accept="video/*" @change="handleFileUpload" />
              </label>
              <p class="mt-1 text-xs text-gray-500">
                支持 MP4, MOV, AVI 等格式，单个文件最大 2GB
              </p>
            </div>
          </div>
          
          <div class="flex justify-end space-x-3 mt-6">
            <button
              @click="showUploadModal = false"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useTaskStore } from '@/stores/tasks'
import { useVideoStore } from '@/stores/videos'

const router = useRouter()
const appStore = useAppStore()
const taskStore = useTaskStore()
const videoStore = useVideoStore()

// 响应式数据
const loadingVideos = ref(false)
const videos = ref([])
const selectedVideo = ref(null)
const showUploadModal = ref(false)
const currentTaskId = ref(1) // 假设当前任务ID为1，实际应该从路由或其他地方获取

// 视频播放相关
const videoPlayer = ref(null)
const videoLoading = ref(false)
const videoError = ref(false)

// 分析步骤定义
const analysisSteps = ref([
  { id: 1, name: '基础信息分析', statusKey: 'basic_analysis_status' },
  { id: 2, name: '内容要素分析', statusKey: 'content_analysis_status' },
  { id: 3, name: '剧情分析', statusKey: 'plot_analysis_status' }
])

// 方法
const loadVideos = async () => {
  loadingVideos.value = true
  try {
    videos.value = await videoStore.fetchVideos()
  } catch (error) {
    appStore.showError('加载失败', '无法加载视频列表')
  } finally {
    loadingVideos.value = false
  }
}

const selectVideo = (video) => {
  console.log('selectVideo: 选择视频', video)
  selectedVideo.value = video
  // 重置视频播放状态
  videoLoading.value = false
  videoError.value = false
}

const startAnalysis = async () => {
  try {
    await videoStore.startAnalysis(selectedVideo.value.id)
    selectedVideo.value.status = 'analyzing'
    appStore.showSuccess('分析开始', '视频分析任务已启动')
  } catch (error) {
    appStore.showError('启动失败', '无法启动分析任务')
  }
}

const viewAnalysisDetail = () => {
  router.push(`/analysis/${selectedVideo.value.id}`)
}

const handleFileUpload = async (event) => {
  const files = Array.from(event.target.files)
  if (files.length > 0) {
    showUploadModal.value = false

    for (const file of files) {
      try {
        appStore.showInfo('上传中', `正在上传 ${file.name}...`)
        await taskStore.uploadVideo(currentTaskId.value, file)
        appStore.showSuccess('上传成功', `${file.name} 上传完成`)
      } catch (error) {
        appStore.showError('上传失败', `${file.name} 上传失败`)
      }
    }

    // 重新加载视频列表
    await loadVideos()
  }
}

const getStatusColor = (status) => {
  const colors = {
    pending: 'bg-gray-300',
    processing: 'bg-blue-500',
    completed: 'bg-green-500',
    failed: 'bg-red-500'
  }
  return colors[status] || 'bg-gray-300'
}

const getStatusText = (status) => {
  const texts = {
    pending: '等待中',
    processing: '分析中',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || '未知'
}



const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 视频相关方法
const getVideoUrl = (video) => {
  if (!video || !video.filename) {
    console.log('getVideoUrl: 无视频或文件名', video)
    return ''
  }
  const url = `http://localhost:8000/static/uploads/${video.filename}`
  console.log('getVideoUrl: 生成视频URL', url)
  return url
}

const getVideoThumbnail = (video) => {
  if (!video || !video.thumbnail_path) return ''
  return `http://localhost:8000/static/thumbnails/${video.thumbnail_path}`
}

const onVideoLoaded = () => {
  videoLoading.value = false
  videoError.value = false
  console.log('视频加载完成')
}

const onVideoError = (event) => {
  videoLoading.value = false
  videoError.value = true
  console.error('视频加载失败:', event)
  appStore.showError('视频加载失败', '无法播放该视频文件')
}

const retryVideoLoad = () => {
  videoError.value = false
  videoLoading.value = true
  if (videoPlayer.value) {
    videoPlayer.value.load()
  }
}

// 生命周期
onMounted(() => {
  loadVideos()
})
</script>
